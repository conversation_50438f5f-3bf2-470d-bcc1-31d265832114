app:
  description: 智能美食推荐和菜谱查询助手
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 美食助手_智能分析
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.27@4bb3f3eb6149b01f92aa6038a6bb074cc4224b8c015f54c888e5c5a30fc1ab50
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: 您好！我是您的专属美食助手，可以为您推荐菜品、查询菜谱，或根据口味偏好找到合适的美食。请告诉我您想要什么样的菜品吧！
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 推荐几道川菜
    - 想吃点清淡的菜
    - 鱼香肉丝怎么做？
    - 有什么下饭的菜？
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: 1750836018035-dialogue-llm
      source: '1750836018035'
      sourceHandle: source
      target: dialogue-llm
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: llm
      id: dialogue-llm-llm
      source: dialogue-llm
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: if-else
      id: llm-condition
      source: llm
      sourceHandle: source
      target: condition
      targetHandle: target
      type: custom
    - data:
        sourceType: if-else
        targetType: knowledge-retrieval
      id: condition-1750836027842-true
      source: condition
      sourceHandle: 'true'
      target: '1750836027842'
      targetHandle: target
      type: custom
    - data:
        sourceType: if-else
        targetType: knowledge-retrieval
      id: condition-knowledge2-false
      source: condition
      sourceHandle: 'false'
      target: knowledge2
      targetHandle: target
      type: custom
    - data:
        sourceType: knowledge-retrieval
        targetType: variable-aggregator
      id: 1750836027842-aggregator
      source: '1750836027842'
      sourceHandle: source
      target: aggregator
      targetHandle: target
      type: custom
    - data:
        sourceType: knowledge-retrieval
        targetType: variable-aggregator
      id: knowledge2-aggregator
      source: knowledge2
      sourceHandle: source
      target: aggregator
      targetHandle: target
      type: custom
    - data:
        sourceType: variable-aggregator
        targetType: llm
      id: aggregator-1750836031072
      source: aggregator
      sourceHandle: source
      target: '1750836031072'
      targetHandle: target
      type: custom
    - data:
        sourceType: llm
        targetType: answer
      id: 1750836031072-1750836034089
      source: '1750836031072'
      sourceHandle: source
      target: '1750836034089'
      targetHandle: target
      type: custom
    nodes:
    - data:
        desc: 工作流起始点
        selected: false
        title: 开始
        type: start
        variables: []
      height: 81
      id: '1750836018035'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 理解用户对话意图
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: true
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: dialogue-understanding-prompt
          role: system
          text: '# 角色定义

            你是一个对话理解专家，负责理解用户的对话上下文和真实意图。


            ## 任务

            1. 分析用户的当前输入：{{#sys.query#}}

            2. 结合历史对话上下文

            3. 输出用户的核心需求


            ## 输出要求

            输出清晰的用户意图描述，包括：

            - 用户想要做什么

            - 任何特殊的偏好或限制

            - 相关的上下文信息

            '
        selected: false
        title: 对话理解LLM
        type: llm
        variables: []
        vision:
          enabled: false
      height: 123
      id: dialogue-llm
      position:
        x: 360
        y: 282
      positionAbsolute:
        x: 360
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - dialogue-llm
          - text
        desc: 分析用户输入，提取美食相关关键信息
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: ba95171b-7191-4f3c-924c-e7c77eb3c238
          role: system
          text: "# 角色定义\n你是一个精通美食的AI模型，擅长解析用户的口语化对话。\n\n## 核心原则\n理解并超越字面含义。你的首要任务是听懂用户的真实需求，即便是模糊的、描述性的语言。\n\
            \n## 用户输入\n原始输入：{{#sys.query#}}\n意图理解：{{#dialogue-llm.text#}}\n\n## 任务\n\
            从用户的输入中，精准提取与美食、菜谱、菜系及口味相关的核心信息。\n\n## 提取规则\n\n**意图 (intent)**: 分析用户的核心目的。必须是以下几种之一：\n\
            - recommend_dish: 用户希望推荐菜品\n- get_recipe: 用户想查询某个具体菜品的做法\n- search_by_cuisine:\
            \ 用户明确或暗示想在某个菜系下查找菜品\n\n**菜品 (dish)**: 具体的菜品名称，如果没有则为 null\n**菜系 (cuisine)**:\
            \ 菜系类别，如果没有则为 null\n**口味 (taste)**: 口味偏好，如果没有则为 null\n**search_type**:\
            \ 根据意图判断，如果是get_recipe输出\"specific\"，否则输出\"general\"\n\n## 输出格式\n```json\n\
            {\n  \"intent\": \"...\",\n  \"dish\": \"...\",\n  \"cuisine\": \"...\"\
            ,\n  \"taste\": \"...\",\n  \"search_type\": \"...\"\n}\n```\n"
        selected: false
        title: 关键词提取
        type: llm
        variables: []
        vision:
          enabled: false
      height: 123
      id: llm
      position:
        x: 640
        y: 282
      positionAbsolute:
        x: 640
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: check-search-type
            value: '"search_type": "specific"'
            variable_selector:
            - llm
            - text
          logical_operator: and
        conditions:
        - comparison_operator: contains
          id: check-search-type
          value: '"search_type": "specific"'
          variable_selector:
          - llm
          - text
        desc: 根据search_type判断知识库
        logical_operator: and
        selected: false
        title: 判断查询类型
        type: if-else
      height: 153
      id: condition
      position:
        x: 920
        y: 282
      positionAbsolute:
        x: 920
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids: []
        desc: 菜谱.pdf...
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 5
        query_variable_selector:
        - llm
        - text
        retrieval_mode: multiple
        selected: false
        title: 知识检索
        type: knowledge-retrieval
      height: 81
      id: '1750836027842'
      position:
        x: 1200
        y: 200
      positionAbsolute:
        x: 1200
        y: 200
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        dataset_ids: []
        desc: 完整菜品知识库
        multiple_retrieval_config:
          reranking_enable: true
          reranking_mode: reranking_model
          reranking_model:
            model: netease-youdao/bce-reranker-base_v1
            provider: langgenius/siliconflow/siliconflow
          top_k: 10
        query_variable_selector:
        - llm
        - text
        retrieval_mode: multiple
        selected: true
        title: 知识检索 2
        type: knowledge-retrieval
      height: 81
      id: knowledge2
      position:
        x: 1200
        y: 360
      positionAbsolute:
        x: 1200
        y: 360
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        advanced_settings:
          group_enabled: false
        desc: 聚合检索结果
        selected: false
        title: 变量聚合器
        type: variable-aggregator
        variable_selector:
        - sys
        - query
        variables: []
      height: 136
      id: aggregator
      position:
        x: 1480
        y: 282
      positionAbsolute:
        x: 1480
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: true
          variable_selector:
          - aggregator
          - output
        desc: 将检索结果整理成专业的美食建议
        model:
          completion_params: {}
          mode: chat
          name: deepseek-v3
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 9b70494a-5c74-49dc-9221-f9c28ae35f18
          role: system
          text: "# 角色定义\n你是一位热情、专业的美食专家。你的沟通风格既友好又可靠，善于将复杂的菜谱变得清晰易懂。\n\n## 必须参考的内容\n\
            \n**（1）知识库检索结果**\n{{#context#}}\n\n**（2）用户理解分析**\n{{#dialogue-llm.text#}}\n\
            \n**（3）关键词提取**\n{{#llm.text#}}\n\n**（4）用户原始输入**  \n{{#sys.query#}}\n\n\
            ## 任务\n根据以上所有信息，生成一份专业、友好、实用的美食建议回复。\n\n## 输出规则\n\n### 1. 推荐菜品列表\n如果是推荐类查询：\n\
            - 以友好的引导句开始\n- 使用清晰的列表格式展示菜品\n- 每个菜品附带简短的特色说明\n- 根据用户偏好过滤不合适的菜品\n\n###\
            \ 2. 具体菜谱展示\n如果是菜谱查询：\n- 清晰报出菜品名称\n- 结构化展示：难度、时间、材料、步骤\n- 提供实用的烹饪小贴士\n\
            - 鼓励性的结语\n\n### 3. 无结果处理\n如果没有找到合适的结果：\n- 礼貌说明情况\n- 提供相近的替代建议\n- 引导用户尝试其他查询方式\n"
        selected: false
        title: 美食专家
        type: llm
        variables: []
        vision:
          enabled: false
      height: 123
      id: '1750836031072'
      position:
        x: 1760.9999999999998
        y: 282
      positionAbsolute:
        x: 1760.9999999999998
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1750836031072.text#}}'
        desc: 向用户输出最终回答
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 132
      id: '1750836034089'
      position:
        x: 2040
        y: 282
      positionAbsolute:
        x: 2040
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -541.6067039250211
      y: 132.38255419033035
      zoom: 0.8705505632961267
